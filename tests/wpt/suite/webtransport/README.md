# Notes about WebTransport WPTs

### Running the WebTransport WPTs using the test server
Although these tests do not currently run on the Chromium CI, they can still be
run using the WebTransport WPT server manually.

Please refer to the following document for detailed instructions:
[Running WPT WebTransport test server](https://docs.google.com/document/d/1OBoZTcC9vDoLTgv_5WUznRFrmwXP0Gprj7V9oOzH9cU/edit?usp=sharing)


### Server Handlers
The python server handlers are stored under `handlers/` and are written using the
PEP8 style.
For details, please see the [style guide](https://www.python.org/dev/peps/pep-0008/).
