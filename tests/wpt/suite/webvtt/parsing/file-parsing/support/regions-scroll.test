regions, scroll
<link rel="help" href="https://w3c.github.io/webvtt/#collect-webvtt-region-settings">

assert_equals(cues.length, 6);

var regions = Array.from(cues).map(function(cue) {
    return cue.region;
});

var valid_lines = [
    '',
    'up',
    'up',
    '',
    '',
    'up',
].forEach(function(valid, index) {
    assert_equals(regions[index].scroll, valid, 'Failed with region ' + index);
});

===
WEBVTT

REGION
id:0

REGION
id:1
scroll:up

REGION
id:2
scroll:up scroll:up scroll:up scroll:up scroll:up scroll:up scroll:up scroll:up
scroll:up scroll:up scroll:up scroll:up scroll:up scroll:up scroll:up scroll:up

REGION
id:3
scroll:down
scroll:left
scroll:right

REGION
id:4
scroll: up
scroll :up

REGION
id:5
scroll:up scroll:

00:00:00.000 --> 00:00:01.000 region:0
text

00:00:00.000 --> 00:00:01.000 region:1
text

00:00:00.000 --> 00:00:01.000 region:2
text

00:00:00.000 --> 00:00:01.000 region:3
text

00:00:00.000 --> 00:00:01.000 region:4
text

00:00:00.000 --> 00:00:01.000 region:5
text
