timings, garbage
<link rel="help" href="https://w3c.github.io/webvtt/#collect-a-webvtt-timestamp">
<link rel="help" href="https://w3c.github.io/webvtt/#collect-webvtt-cue-timings-and-settings">

assert_equals(cues.length, 0);

===
WEBVTT

x00:00:00.000 --> 00:00:01.000
invalid

0x0:00:00.000 --> 00:00:01.000
invalid

00x:00:00.000 --> 00:00:01.000
invalid

00:x00:00.000 --> 00:00:01.000
invalid

00:0x0:00.000 --> 00:00:01.000
invalid

00:00x:00.000 --> 00:00:01.000
invalid

00:00:x00.000 --> 00:00:01.000
invalid

00:00:0x0.000 --> 00:00:01.000
invalid

00:00:00x.000 --> 00:00:01.000
invalid

00:00:00.x000 --> 00:00:01.000
invalid

00:00:00.0x00 --> 00:00:01.000
invalid

00:00:00.00x0 --> 00:00:01.000
invalid

00:00:00.000x --> 00:00:01.000
invalid

00:00:00.000 x--> 00:00:01.000
invalid

00:00:00.000 -x-> 00:00:01.000
invalid

00:00:00.000 --x> 00:00:01.000
invalid

00:00:00.000 -->x 00:00:01.000
invalid

00:00:00.000 --> x00:00:01.000
invalid

00:00:00.000 --> 0x0:00:01.000
invalid

00:00:00.000 --> 00x:00:01.000
invalid

00:00:00.000 --> 00:x00:01.000
invalid

00:00:00.000 --> 00:0x0:01.000
invalid

00:00:00.000 --> 00:00x:01.000
invalid

00:00:00.000 --> 00:00:x01.000
invalid

00:00:00.000 --> 00:00:0x1.000
invalid

00:00:00.000 --> 00:00:01x.000
invalid

00:00:00.000 --> 00:00:01.x000
invalid

00:00:00.000 --> 00:00:01.0x00
invalid

00:00:00.000 --> 00:00:01.00x0
invalid

x0:00:00.000 --> 00:00:01.000
invalid

0x:00:00.000 --> 00:00:01.000
invalid

00x00:00.000 --> 00:00:01.000
invalid

00:x0:00.000 --> 00:00:01.000
invalid

00:0x:00.000 --> 00:00:01.000
invalid

00:00x00.000 --> 00:00:01.000
invalid

00:00:x0.000 --> 00:00:01.000
invalid

00:00:0x.000 --> 00:00:01.000
invalid

00:00:00x000 --> 00:00:01.000
invalid

00:00:00.x00 --> 00:00:01.000
invalid

00:00:00.0x0 --> 00:00:01.000
invalid

00:00:00.00x --> 00:00:01.000
invalid

00:00:00.000x--> 00:00:01.000
invalid

00:00:00.000 x-> 00:00:01.000
invalid

00:00:00.000 -x> 00:00:01.000
invalid

00:00:00.000 --x 00:00:01.000
invalid

00:00:00.000 -->x00:00:01.000
invalid

00:00:00.000 --> x0:00:01.000
invalid

00:00:00.000 --> 0x:00:01.000
invalid

00:00:00.000 --> 00x00:01.000
invalid

00:00:00.000 --> 00:x0:01.000
invalid

00:00:00.000 --> 00:0x:01.000
invalid

00:00:00.000 --> 00:00x01.000
invalid

00:00:00.000 --> 00:00:x1.000
invalid

00:00:00.000 --> 00:00:0x.000
invalid

00:00:00.000 --> 00:00:01x000
invalid

00:00:00.000 --> 00:00:01.x00
invalid

00:00:00.000 --> 00:00:01.0x0
invalid

00:00:00.000 --> 00:00:01.00x
invalid

00.00:00.000 --> 00:00:01.000
invalid

00:00.00.000 --> 00:00:01.000
invalid

00:00:00:000 --> 00:00:01.000
invalid

00:00.00:000 --> 00:00:01.000
invalid

00:00:00,000 --> 00:00:01,000
invalid