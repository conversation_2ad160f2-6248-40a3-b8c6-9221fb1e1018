regions, viewportanchor
<link rel="help" href="https://w3c.github.io/webvtt/#collect-webvtt-region-settings">

assert_equals(cues.length, 20);

var regions = Array.from(cues).map(function(cue) {
    return cue.region;
});

var valid_anchors = [
    [0, 100],
    [0, 0],
    [1, 1],
    [100, 0],
    [0, 100],
    [100, 100],
];
valid_anchors.forEach(function(pair, index) {
    var anchorX = pair[0];
    var anchorY = pair[1];

    assert_equals(regions[index].viewportAnchorX, anchorX, 'Failed with region ' + index);
    assert_equals(regions[index].viewportAnchorY, anchorY, 'Failed with region ' + index);
});

for (var i = 0; i < 14; i++) {
    var index = valid_anchors.length + i;

    assert_equals(regions[index].viewportAnchorX, 0, 'Failed with region ' + index);
    assert_equals(regions[index].viewportAnchorY, 100, 'Failed with region ' + index);
}

===
WEBVTT

NOTE valid

REGION
id:0

REGION
id:1
viewportanchor:0%,0%

REGION
id:2
viewportanchor:1%,1%

REGION
id:3
viewportanchor:100%,0%

REGION
id:4
viewportanchor:0%,100%

REGION
id:5
viewportanchor:100%,100%

NOTE invalid

REGION
id:6
viewportanchor:0,0

REGION
id:7
viewportanchor:0%,0

REGION
id:8
viewportanchor:0,0%

REGION
id:9
viewportanchor:1%

REGION
id:10
viewportanchor:,1%

REGION
id:11
viewportanchor:101%,1%

REGION
id:12
viewportanchor:1%,101%

REGION
id:13
viewportanchor:-0%,0%

REGION
id:14
viewportanchor:0%,-0%

REGION
id:15
viewportanchor:65536%,65536%

REGION
id:16
viewportanchor:4294967296%,4294967296%

REGION
id:17
viewportanchor:18446744073709552000%,18446744073709552000%

REGION
id:18
viewportanchor:10000000000000000000000000000000000%,10000000000000000000000000000000000%

REGION
id:19
viewportanchor: 100%,100%
viewportanchor :100%,100%
viewportanchor:100% ,100%
viewportanchor:100%, 100%
viewportanchor:100 %,100%
viewportanchor:100%,100 %

00:00:00.000 --> 00:00:01.000 region:0
text

00:00:00.000 --> 00:00:01.000 region:1
text

00:00:00.000 --> 00:00:01.000 region:2
text

00:00:00.000 --> 00:00:01.000 region:3
text

00:00:00.000 --> 00:00:01.000 region:4
text

00:00:00.000 --> 00:00:01.000 region:5
text

00:00:00.000 --> 00:00:01.000 region:6
text

00:00:00.000 --> 00:00:01.000 region:7
text

00:00:00.000 --> 00:00:01.000 region:8
text

00:00:00.000 --> 00:00:01.000 region:9
text

00:00:00.000 --> 00:00:01.000 region:10
text

00:00:00.000 --> 00:00:01.000 region:11
text

00:00:00.000 --> 00:00:01.000 region:12
text

00:00:00.000 --> 00:00:01.000 region:13
text

00:00:00.000 --> 00:00:01.000 region:14
text

00:00:00.000 --> 00:00:01.000 region:15
text

00:00:00.000 --> 00:00:01.000 region:16
text

00:00:00.000 --> 00:00:01.000 region:17
text

00:00:00.000 --> 00:00:01.000 region:18
text

00:00:00.000 --> 00:00:01.000 region:19
text
