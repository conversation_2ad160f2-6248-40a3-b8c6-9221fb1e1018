<!doctype html>
<title>VTTCue.getCueAsHTML()</title>
<link rel="help" href="https://w3c.github.io/webvtt/#dom-vttcue-getcueashtml">
<script src=/resources/testharness.js></script>
<script src=/resources/testharnessreport.js></script>
<div id=log></div>
<script>
test(function(){
    var video = document.createElement('video');
    var t1 = video.addTextTrack('subtitles');
    document.body.appendChild(video);
    var c1 = new VTTCue(0, 1, '<c></c><c.a.b></c><i></i><b></b><u></u><ruby><rt></rt></ruby><v></v><v a b></v><1:00:00.500>x\0');
    t1.addCue(c1);
    window.frag = c1.getCueAsHTML();
    assert_equals(frag.childNodes.length, 10, 'childNodes.length');
    assert_true(frag instanceof DocumentFragment, 'getCueAsHTML() should return DocumentFragment');
}, document.title+', creating the cue');
test(function(){
    assert_equals(frag.childNodes[0].namespaceURI, 'http://www.w3.org/1999/xhtml', 'namespaceURI');
    assert_equals(frag.childNodes[0].localName, 'span', 'localName');
    assert_equals(frag.childNodes[0].attributes.length, 0, 'attributes');
    assert_false(frag.childNodes[0].hasChildNodes(), 'hasChildNodes()');
    assert_true(frag.childNodes[0] instanceof HTMLElement, 'instanceof');
}, document.title+', <c>');
test(function(){
    assert_equals(frag.childNodes[1].namespaceURI, 'http://www.w3.org/1999/xhtml', 'namespaceURI');
    assert_equals(frag.childNodes[1].localName, 'span', 'localName');
    assert_equals(frag.childNodes[1].attributes.length, 1, 'attributes');
    assert_equals(frag.childNodes[1].getAttributeNS('', 'class'), 'a b', 'class attribute');
    assert_false(frag.childNodes[1].hasChildNodes(), 'hasChildNodes()');
    assert_true(frag.childNodes[1] instanceof HTMLElement, 'instanceof');
}, document.title+', <c.a.b>');
test(function(){
    assert_equals(frag.childNodes[2].namespaceURI, 'http://www.w3.org/1999/xhtml', 'namespaceURI');
    assert_equals(frag.childNodes[2].localName, 'i', 'localName');
    assert_equals(frag.childNodes[2].attributes.length, 0, 'attributes');
    assert_false(frag.childNodes[2].hasChildNodes(), 'hasChildNodes()');
    assert_true(frag.childNodes[2] instanceof HTMLElement, 'instanceof');
}, document.title+', <i>');
test(function(){
    assert_equals(frag.childNodes[3].namespaceURI, 'http://www.w3.org/1999/xhtml', 'namespaceURI');
    assert_equals(frag.childNodes[3].localName, 'b', 'localName');
    assert_equals(frag.childNodes[3].attributes.length, 0, 'attributes');
    assert_false(frag.childNodes[3].hasChildNodes(), 'hasChildNodes()');
    assert_true(frag.childNodes[3] instanceof HTMLElement, 'instanceof');
}, document.title+', <b>');
test(function(){
    assert_equals(frag.childNodes[4].namespaceURI, 'http://www.w3.org/1999/xhtml', 'namespaceURI');
    assert_equals(frag.childNodes[4].localName, 'u', 'localName');
    assert_equals(frag.childNodes[4].attributes.length, 0, 'attributes');
    assert_false(frag.childNodes[4].hasChildNodes(), 'hasChildNodes()');
    assert_true(frag.childNodes[4] instanceof HTMLElement, 'instanceof');
}, document.title+', <u>');
test(function(){
    assert_equals(frag.childNodes[5].namespaceURI, 'http://www.w3.org/1999/xhtml', 'namespaceURI');
    assert_equals(frag.childNodes[5].localName, 'ruby', 'localName');
    assert_equals(frag.childNodes[5].attributes.length, 0, 'attributes');
    assert_true(frag.childNodes[5].hasChildNodes(), 'hasChildNodes()');
    assert_true(frag.childNodes[5] instanceof HTMLElement, 'instanceof');
}, document.title+', <ruby>');
test(function(){
    assert_equals(frag.childNodes[5].firstChild.namespaceURI, 'http://www.w3.org/1999/xhtml', 'namespaceURI');
    assert_equals(frag.childNodes[5].firstChild.localName, 'rt', 'localName');
    assert_equals(frag.childNodes[5].firstChild.attributes.length, 0, 'attributes');
    assert_false(frag.childNodes[5].firstChild.hasChildNodes(), 'hasChildNodes()');
    assert_true(frag.childNodes[5].firstChild instanceof HTMLElement, 'instanceof');
}, document.title+', <rt>');
test(function(){
    assert_equals(frag.childNodes[6].namespaceURI, 'http://www.w3.org/1999/xhtml', 'namespaceURI');
    assert_equals(frag.childNodes[6].localName, 'span', 'localName');
    assert_equals(frag.childNodes[6].attributes.length, 1, 'attributes');
    assert_equals(frag.childNodes[6].getAttributeNS('', 'title'), '', 'title attribute');
    assert_false(frag.childNodes[6].hasChildNodes(), 'hasChildNodes()');
    assert_true(frag.childNodes[6] instanceof HTMLElement, 'instanceof');
}, document.title+', <v>');
test(function(){
    assert_equals(frag.childNodes[7].namespaceURI, 'http://www.w3.org/1999/xhtml', 'namespaceURI');
    assert_equals(frag.childNodes[7].localName, 'span', 'localName');
    assert_equals(frag.childNodes[7].attributes.length, 1, 'attributes');
    assert_equals(frag.childNodes[7].getAttributeNS('', 'title'), 'a b', 'title attribute');
    assert_false(frag.childNodes[7].hasChildNodes(), 'hasChildNodes()');
    assert_true(frag.childNodes[7] instanceof HTMLElement, 'instanceof');
}, document.title+', <v a b>');
test(function(){
    assert_equals(frag.childNodes[8].target, 'timestamp', 'target');
    assert_equals(frag.childNodes[8].data, '01:00:00.500', 'data');
    assert_true(frag.childNodes[8] instanceof ProcessingInstruction, 'instanceof');
}, document.title+', <1:00:00.500>');
test(function(){
    assert_equals(frag.childNodes[9].data, 'x\0', 'data');
    assert_true(frag.childNodes[9] instanceof Text, 'instanceof');
}, document.title+', x\\0');
</script>
