<!doctype html>
<title>VTTRegion.viewportAnchorX</title>
<link rel="help" href="https://w3c.github.io/webvtt/#dom-vttregion-viewportanchorx">
<script src=/resources/testharness.js></script>
<script src=/resources/testharnessreport.js></script>
<div id=log></div>
<script>
test(function() {
    var region = new VTTRegion();
    assert_true('viewportAnchorX' in region, 'viewportAnchorX is not supported');

    for (var i = 0; i <= 100; i++) {
        region.viewportAnchorX = i;
        assert_equals(region.viewportAnchorX, i);
    }

    region.viewportAnchorX = 1.5;
    assert_equals(region.viewportAnchorX, 1.5);

    [-1, -100, -150, 101, 200, 250].forEach(function (invalid) {
        assert_throws_dom('IndexSizeError', function() {
            region.viewportAnchorX = invalid;
        });
    });
}, document.title + ' script-created region');
</script>
