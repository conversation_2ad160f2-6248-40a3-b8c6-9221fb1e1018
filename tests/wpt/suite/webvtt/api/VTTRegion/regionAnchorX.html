<!doctype html>
<title>VTTRegion.regionAnchorX</title>
<link rel="help" href="https://w3c.github.io/webvtt/#dom-vttregion-regionanchorx">
<script src=/resources/testharness.js></script>
<script src=/resources/testharnessreport.js></script>
<div id=log></div>
<script>
test(function() {
    var region = new VTTRegion();
    assert_true('regionAnchorX' in region, 'regionAnchorX is not supported');

    for (var i = 0; i <= 100; i++) {
        region.regionAnchorX = i;
        assert_equals(region.regionAnchorX, i);
    }

    region.regionAnchorX = 1.5;
    assert_equals(region.regionAnchorX, 1.5);

    [-1, -100, -150, 101, 200, 250].forEach(function (invalid) {
        assert_throws_dom('IndexSizeError', function() {
            region.regionAnchorX = invalid;
        });
    });
}, document.title + ' script-created region');
</script>
