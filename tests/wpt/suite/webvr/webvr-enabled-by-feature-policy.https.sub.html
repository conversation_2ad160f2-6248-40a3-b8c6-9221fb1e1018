<!DOCTYPE html>
<body>
  <script src=/resources/testharness.js></script>
  <script src=/resources/testharnessreport.js></script>
  <script src=/feature-policy/resources/featurepolicy.js></script>

  <script>
    'use strict';
    var same_origin_src = '/feature-policy/resources/feature-policy-webvr.html';
    var cross_origin_src = 'https://{{domains[www]}}:{{ports[https][0]}}' +
      same_origin_src;
    var header = 'Feature-Policy header vr *';

    promise_test(
        () => navigator.getVRDisplays(),
        header + ' allows the top-level document.');

    async_test(t => {
      test_feature_availability(
          'navigator.getVRDisplays()', t, same_origin_src,
          expect_feature_available_default);
    }, header + ' allows same-origin iframes.');

    async_test(t => {
      test_feature_availability(
          'navigator.getVRDisplays()', t, cross_origin_src,
          expect_feature_available_default);
    }, header + ' allows cross-origin iframes.');
  </script>
</body>
