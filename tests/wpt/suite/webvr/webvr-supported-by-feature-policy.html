<!DOCTYPE html>
<title>Test that (obsolete) vr is advertised in the feature list</title>
<!-- Some WebVR implementations used "vr", but this is now obsolete and WebXR is moving in a different direction.-->>
<link rel="help" href="https://github.com/immersive-web/webxr/issues/308">
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script>
test(() => {
    assert_in_array('vr', document.featurePolicy.features());
}, 'document.featurePolicy.features should advertise (obsolete) vr.');
</script>
