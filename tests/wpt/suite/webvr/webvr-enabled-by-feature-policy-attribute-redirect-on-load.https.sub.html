<!DOCTYPE html>
<body>
  <script src=/resources/testharness.js></script>
  <script src=/resources/testharnessreport.js></script>
  <script src=/feature-policy/resources/featurepolicy.js></script>
  <script>
    'use strict';
    var relative_path = '/feature-policy/resources/feature-policy-webvr.html';
    var base_src = '/feature-policy/resources/redirect-on-load.html#';
    var same_origin_src = base_src + relative_path;
    var cross_origin_src = base_src + 'https://{{domains[www]}}:{{ports[https][0]}}' +
        relative_path;
    var header = 'Feature-Policy allow="vr" attribute';

    async_test(t => {
      test_feature_availability(
          'navigator.getVRDisplays()', t, same_origin_src,
          expect_feature_available_default, 'vr');
    }, header + ' allows same-origin relocation');

    async_test(t => {
      test_feature_availability(
          'navigator.getVRDisplays()', t, cross_origin_src,
          expect_feature_unavailable_default, 'vr');
    }, header + ' disallows cross-origin relocation');
  </script>
</body>
