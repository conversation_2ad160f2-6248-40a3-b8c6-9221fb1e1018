<!DOCTYPE html>
<body>
<script src=/resources/testharness.js></script>
<script src=/resources/testharnessreport.js></script>
<script src=/permissions-policy/resources/permissions-policy.js></script>
<script>
'use strict';
const sub = 'https://{{domains[www]}}:{{ports[https][0]}}';
const same_origin_src = '/permissions-policy/resources/permissions-policy-usb.html';
const cross_origin_src = sub + same_origin_src;
const same_origin_worker_frame_src =
    '/permissions-policy/resources/permissions-policy-usb-worker.html';
const cross_origin_worker_frame_src = sub + same_origin_worker_frame_src;
const header = 'Permissions-Policy header usb=*';

promise_test(
    () => navigator.usb.getDevices(),
    header + ' allows the top-level document.');

async_test(t => {
  test_feature_availability('usb.getDevices()', t, same_origin_src,
      expect_feature_available_default);
}, header + ' allows same-origin iframes.');

async_test(t => {
  test_feature_availability('usb.getDevices()', t, same_origin_worker_frame_src,
      expect_feature_available_default);
}, header + ' allows workers in same-origin iframes.');

// Set allow="usb" on iframe element to delegate 'usb' to cross origin subframe.
async_test(t => {
  test_feature_availability('usb.getDevices()', t, cross_origin_src,
      expect_feature_available_default, 'usb');
}, header + ' allows cross-origin iframes.');

// Set allow="usb" on iframe element to delegate 'usb' to cross origin subframe.
async_test(t => {
  test_feature_availability('usb.getDevices()', t,
      cross_origin_worker_frame_src,
      expect_feature_available_default, 'usb');
}, header + ' allows workers in cross-origin iframes.');

fetch_tests_from_worker(new Worker(
    '/webusb/resources/usb-allowed-by-permissions-policy-worker.js'));
</script>
</body>
