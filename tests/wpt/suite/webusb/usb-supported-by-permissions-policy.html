<!DOCTYPE html>
<title>Test that usb is advertised in the feature list</title>
<link rel="help" href="https://w3c.github.io/webappsec-permissions-policy/#dom-permissionspolicy-features">
<link rel="help" href="https://wicg.github.io/webusb/#permissions-policy">
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script>
test(() => {
    assert_in_array('usb', document.featurePolicy.features());
}, 'document.featurePolicy.features should advertise usb.');
</script>
