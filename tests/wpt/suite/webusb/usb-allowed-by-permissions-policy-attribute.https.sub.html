<!DOCTYPE html>
<body>
<script src=/resources/testharness.js></script>
<script src=/resources/testharnessreport.js></script>
<script src=/permissions-policy/resources/permissions-policy.js></script>
<script>
'use strict';
const sub = 'https://{{domains[www]}}:{{ports[https][0]}}';
const same_origin_src = '/permissions-policy/resources/permissions-policy-usb.html';
const cross_origin_src = sub + same_origin_src;
const same_origin_worker_frame_src =
    '/permissions-policy/resources/permissions-policy-usb-worker.html';
const cross_origin_worker_frame_src = sub + same_origin_worker_frame_src;
const feature_name = 'Permissions policy "usb"';
const header = 'allow="usb" attribute';

async_test(t => {
  test_feature_availability(
      'usb.getDevices()', t, same_origin_src,
      expect_feature_available_default, 'usb');
}, feature_name + ' can be enabled in same-origin iframe using ' + header);

async_test(t => {
  test_feature_availability(
      'usb.getDevices()', t, same_origin_worker_frame_src,
      expect_feature_available_default, 'usb');
}, feature_name + ' can be enabled in a worker in same-origin iframe using ' +
    header);

async_test(t => {
  test_feature_availability(
      'usb.getDevices()', t, cross_origin_src,
      expect_feature_available_default, 'usb');
}, feature_name + ' can be enabled in cross-origin iframe using ' + header);

async_test(t => {
  test_feature_availability(
      'usb.getDevices()', t, cross_origin_worker_frame_src,
      expect_feature_available_default, 'usb');
}, feature_name + ' can be enabled in a worker in cross-origin iframe using ' +
    header);

fetch_tests_from_worker(new Worker(
    '/webusb/resources/usb-allowed-by-permissions-policy-worker.js'));
</script>
</body>
