<!DOCTYPE html>
<body>
<script src=/resources/testharness.js></script>
<script src=/resources/testharnessreport.js></script>
<script src=/permissions-policy/resources/permissions-policy.js></script>
<script>
'use strict';
var same_origin_src = '/permissions-policy/resources/permissions-policy-usb.html';
var cross_origin_src = 'https://{{domains[www]}}:{{ports[https][0]}}' +
  same_origin_src;
var header = 'Default "usb" permissions policy ["self"]';

promise_test(
    () => navigator.usb.getDevices(),
    header + ' allows the top-level document.');

async_test(t => {
  test_feature_availability('usb.getDevices()', t, same_origin_src,
      expect_feature_available_default);
}, header + ' allows same-origin iframes.');

async_test(t => {
  test_feature_availability('usb.getDevices()', t, cross_origin_src,
      expect_feature_unavailable_default);
}, header + ' disallows cross-origin iframes.');
</script>
</body>
