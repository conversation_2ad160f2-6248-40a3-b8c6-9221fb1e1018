<!DOCTYPE html>
<body>
<script src=/resources/testharness.js></script>
<script src=/resources/testharnessreport.js></script>
<script src=/permissions-policy/resources/permissions-policy.js></script>
<script>
'use strict';
const relative_path = '/permissions-policy/resources/permissions-policy-usb.html';
const base_src = '/permissions-policy/resources/redirect-on-load.html#';
const relative_worker_frame_path =
    '/permissions-policy/resources/permissions-policy-usb-worker.html';
const sub = 'https://{{domains[www]}}:{{ports[https][0]}}';
const same_origin_src = base_src + relative_path;
const cross_origin_src = base_src + sub + relative_path;
const same_origin_worker_frame_src = base_src + relative_worker_frame_path;
const cross_origin_worker_frame_src = base_src + sub +
    relative_worker_frame_path;
const header = 'Permissions-Policy allow="usb"';

async_test(t => {
  test_feature_availability(
      'usb.getDevices()', t, same_origin_src,
      expect_feature_available_default, 'usb');
}, header + ' allows same-origin relocation.');

async_test(t => {
  test_feature_availability(
      'usb.getDevices()', t, same_origin_worker_frame_src,
      expect_feature_available_default, 'usb');
}, header + ' allows workers in same-origin relocation.');

async_test(t => {
  test_feature_availability(
      'usb.getDevices()', t, cross_origin_src,
      expect_feature_unavailable_default, 'usb');
}, header + ' disallows cross-origin relocation.');

async_test(t => {
  test_feature_availability(
      'usb.getDevices()', t, cross_origin_worker_frame_src,
      expect_feature_unavailable_default, 'usb');
}, header + ' disallows workers in cross-origin relocation.');
</script>
</body>
